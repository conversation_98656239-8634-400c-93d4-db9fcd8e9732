using System.Collections.Concurrent;
using GCP.Iot.Models;

namespace GCP.Iot.Services
{
    class EquipmentEventManager
    {
        private readonly ConcurrentDictionary<string, EventHandler<VariableValueChangedEventArgs>> _variableHandlers = new();
        private readonly ConcurrentDictionary<string, EventHandler<EquipmentDataChangedEventArgs>> _equipmentHandlers = new();
        private readonly ConcurrentDictionary<string, EventHandler<EquipmentDataChangedEventArgs>> _equipmentTypeHandlers = new();

        public void AddVariableHandler(string equipmentId, string variableName, EventHandler<VariableValueChangedEventArgs> handler)
        {
            var key = $"{equipmentId}:{variableName}";
            _variableHandlers.AddOrUpdate(key, handler, (_, existing) => existing + handler);
        }

        public void RemoveVariableHandler(string equipmentId, string variableName, EventHandler<VariableValueChangedEventArgs> handler)
        {
            var key = $"{equipmentId}:{variableName}";
            if (_variableHandlers.TryGetValue(key, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _variableHandlers.TryRemove(key, out _);
                }
                else
                {
                    _variableHandlers[key] = existing;
                }
            }
        }

        public void AddEquipmentHandler(string equipmentId, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            _equipmentHandlers.AddOrUpdate(equipmentId, handler, (_, existing) => existing + handler);
        }

        public void RemoveEquipmentHandler(string equipmentId, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            if (_equipmentHandlers.TryGetValue(equipmentId, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _equipmentHandlers.TryRemove(equipmentId, out _);
                }
                else
                {
                    _equipmentHandlers[equipmentId] = existing;
                }
            }
        }

        public void AddEquipmentTypeHandler(string equipmentType, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            _equipmentTypeHandlers.AddOrUpdate(equipmentType, handler, (_, existing) => existing + handler);
        }

        public void RemoveEquipmentTypeHandler(string equipmentType, EventHandler<EquipmentDataChangedEventArgs> handler)
        {
            if (_equipmentTypeHandlers.TryGetValue(equipmentType, out var existing))
            {
                existing -= handler;
                if (existing == null)
                {
                    _equipmentTypeHandlers.TryRemove(equipmentType, out _);
                }
                else
                {
                    _equipmentTypeHandlers[equipmentType] = existing;
                }
            }
        }

        internal void HandleVariableValueChanged(string equipmentId, string variableName, object oldValue, object newValue)
        {
            var key = $"{equipmentId}:{variableName}";
            if (_variableHandlers.TryGetValue(key, out var handler))
            {
                handler?.Invoke(this, new VariableValueChangedEventArgs(equipmentId, variableName, oldValue, newValue));
            }
        }

        internal void HandleEquipmentDataChanged(string equipmentId, string equipmentType, Dictionary<string, object> values)
        {
            var args = new EquipmentDataChangedEventArgs(equipmentId, equipmentType, values);

            // 触发设备级别事件
            if (_equipmentHandlers.TryGetValue(equipmentId, out var equipmentHandler))
            {
                equipmentHandler?.Invoke(this, args);
            }

            // 触发设备类型级别事件
            if (_equipmentTypeHandlers.TryGetValue(equipmentType, out var typeHandler))
            {
                typeHandler?.Invoke(this, args);
            }
        }
    }
}